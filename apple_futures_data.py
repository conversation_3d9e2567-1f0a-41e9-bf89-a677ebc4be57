#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
苹果期货数据获取程序
使用 akshare 库获取苹果期货的实时行情和历史数据

作者: 学习者
日期: 2025-08-05
"""

import akshare as ak
import pandas as pd
import datetime
import os


def get_apple_futures_realtime():
    """
    获取苹果期货实时行情数据
    
    Returns:
        pandas.DataFrame: 苹果期货实时行情数据
    """
    try:
        print("正在获取苹果期货实时行情数据...")
        
        # 获取期货实时行情数据
        # 使用 futures_zh_spot 获取国内期货实时行情
        futures_spot_df = ak.futures_zh_spot(symbol="郑州商品交易所")
        
        # 筛选苹果期货数据（代码包含 AP 的合约）
        apple_futures = futures_spot_df[futures_spot_df['代码'].str.contains('AP', na=False)]
        
        print(f"获取到 {len(apple_futures)} 个苹果期货合约的实时数据")
        return apple_futures
        
    except Exception as e:
        print(f"获取实时数据时出错: {e}")
        return None


def get_apple_futures_historical(symbol="AP0", start_date="20240101", end_date=None):
    """
    获取苹果期货历史数据
    
    Args:
        symbol (str): 期货代码，AP0 表示苹果主力连续合约
        start_date (str): 开始日期，格式 YYYYMMDD
        end_date (str): 结束日期，格式 YYYYMMDD，默认为今天
    
    Returns:
        pandas.DataFrame: 苹果期货历史数据
    """
    try:
        if end_date is None:
            end_date = datetime.datetime.now().strftime("%Y%m%d")
            
        print(f"正在获取苹果期货历史数据...")
        print(f"合约代码: {symbol}")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 获取苹果期货主力连续合约历史数据
        hist_data = ak.futures_main_sina(
            symbol=symbol, 
            start_date=start_date, 
            end_date=end_date
        )
        
        print(f"获取到 {len(hist_data)} 条历史数据")
        return hist_data
        
    except Exception as e:
        print(f"获取历史数据时出错: {e}")
        return None


def get_apple_futures_daily(symbol="AP2405"):
    """
    获取指定苹果期货合约的日线数据
    
    Args:
        symbol (str): 具体的期货合约代码，如 AP2405
    
    Returns:
        pandas.DataFrame: 期货日线数据
    """
    try:
        print(f"正在获取 {symbol} 合约的日线数据...")
        
        # 获取具体合约的日线数据
        daily_data = ak.futures_zh_daily_sina(symbol=symbol)
        
        print(f"获取到 {len(daily_data)} 条日线数据")
        return daily_data
        
    except Exception as e:
        print(f"获取日线数据时出错: {e}")
        return None


def save_data_to_csv(data, filename):
    """
    将数据保存到 CSV 文件
    
    Args:
        data (pandas.DataFrame): 要保存的数据
        filename (str): 文件名
    """
    try:
        # 确保 tempfiles 目录存在
        os.makedirs('tempfiles', exist_ok=True)
        
        filepath = os.path.join('tempfiles', filename)
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filepath}")
        
    except Exception as e:
        print(f"保存数据时出错: {e}")


def display_data_summary(data, title):
    """
    显示数据摘要信息
    
    Args:
        data (pandas.DataFrame): 数据
        title (str): 标题
    """
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    
    if data is not None and not data.empty:
        print(f"数据行数: {len(data)}")
        print(f"数据列数: {len(data.columns)}")
        print(f"\n列名: {list(data.columns)}")
        print(f"\n前5行数据:")
        print(data.head())
        
        # 如果有数值列，显示基本统计信息
        numeric_cols = data.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            print(f"\n数值列统计信息:")
            print(data[numeric_cols].describe())
    else:
        print("数据为空或获取失败")


def main():
    """
    主函数：演示如何获取苹果期货数据
    """
    print("苹果期货数据获取程序启动")
    print("="*60)
    
    # 1. 获取实时行情数据
    realtime_data = get_apple_futures_realtime()
    if realtime_data is not None:
        display_data_summary(realtime_data, "苹果期货实时行情数据")
        save_data_to_csv(realtime_data, "apple_futures_realtime.csv")
    
    # 2. 获取主力连续合约历史数据（最近3个月）
    start_date = (datetime.datetime.now() - datetime.timedelta(days=90)).strftime("%Y%m%d")
    historical_data = get_apple_futures_historical(
        symbol="AP0", 
        start_date=start_date
    )
    if historical_data is not None:
        display_data_summary(historical_data, "苹果期货主力连续合约历史数据")
        save_data_to_csv(historical_data, "apple_futures_historical.csv")
    
    # 3. 获取具体合约的日线数据（以当前主力合约为例）
    # 注意：这里需要根据实际情况调整合约代码
    try:
        # 先尝试获取一个可能的合约代码
        current_month = datetime.datetime.now().month
        current_year = datetime.datetime.now().year % 100  # 取年份后两位
        
        # 苹果期货通常有1、3、5、7、10、11、12月合约
        apple_months = [1, 3, 5, 7, 10, 11, 12]
        next_month = min([m for m in apple_months if m >= current_month], default=apple_months[0])
        
        if next_month < current_month:  # 如果是下一年的合约
            current_year += 1
            
        contract_code = f"AP{current_year:02d}{next_month:02d}"
        print(f"\n尝试获取合约 {contract_code} 的数据...")
        
        daily_data = get_apple_futures_daily(contract_code)
        if daily_data is not None:
            display_data_summary(daily_data, f"苹果期货合约 {contract_code} 日线数据")
            save_data_to_csv(daily_data, f"apple_futures_{contract_code}_daily.csv")
            
    except Exception as e:
        print(f"获取具体合约数据时出错: {e}")
    
    print("\n" + "="*60)
    print("数据获取完成！所有文件已保存到 tempfiles 目录")
    print("="*60)


if __name__ == "__main__":
    main()
