#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据收集器的单元测试

作者: 学习者
日期: 2025-08-05
"""

import unittest
import os
import json
import time
import sys
from unittest.mock import patch, MagicMock

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的模块
import gold_futures_realtime_simple


class TestGoldFuturesRealtimeSimulator(unittest.TestCase):
    """测试沪金期货实时数据模拟器"""
    
    def setUp(self):
        """测试前的设置"""
        self.test_data_dir = 'tempfiles'
        self.simulator = gold_futures_realtime_simple.GoldFuturesRealtimeSimulator()
        
        # 确保测试目录存在
        if not os.path.exists(self.test_data_dir):
            os.makedirs(self.test_data_dir)
    
    def tearDown(self):
        """测试后的清理"""
        # 停止收集器
        if hasattr(self.simulator, 'running') and self.simulator.running:
            self.simulator.stop_collection()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.simulator)
        self.assertEqual(self.simulator.data_dir, 'tempfiles')
        self.assertEqual(self.simulator.max_data_points, 288)
        self.assertFalse(self.simulator.running)
        self.assertEqual(self.simulator.base_price, 782.50)
    
    def test_generate_realistic_price_data(self):
        """测试价格数据生成"""
        data_point = self.simulator.generate_realistic_price_data()
        
        # 验证数据点结构
        self.assertIsNotNone(data_point)
        self.assertIn('timestamp', data_point)
        self.assertIn('contract_code', data_point)
        self.assertIn('contract_name', data_point)
        self.assertIn('price', data_point)
        self.assertIn('change', data_point)
        self.assertIn('change_pct', data_point)
        self.assertIn('volume', data_point)
        self.assertIn('open_interest', data_point)
        self.assertIn('open', data_point)
        self.assertIn('high', data_point)
        self.assertIn('low', data_point)
        self.assertIn('pre_close', data_point)
        
        # 验证数据类型
        self.assertIsInstance(data_point['price'], float)
        self.assertIsInstance(data_point['volume'], int)
        self.assertIsInstance(data_point['contract_code'], str)
        
        # 验证价格范围合理性
        base_price = self.simulator.base_price
        self.assertGreaterEqual(data_point['price'], base_price * 0.9)
        self.assertLessEqual(data_point['price'], base_price * 1.1)
        
        # 验证高低价关系
        self.assertGreaterEqual(data_point['high'], data_point['low'])
        self.assertGreaterEqual(data_point['high'], data_point['price'])
        self.assertLessEqual(data_point['low'], data_point['price'])
    
    def test_collect_data(self):
        """测试数据收集"""
        initial_count = len(self.simulator.realtime_data)
        
        # 收集数据
        self.simulator.collect_data()
        
        # 验证数据增加
        self.assertEqual(len(self.simulator.realtime_data), initial_count + 1)
        
        # 验证最新数据
        latest_data = self.simulator.realtime_data[-1]
        self.assertIn('timestamp', latest_data)
        self.assertIn('price', latest_data)
    
    def test_save_and_load_data(self):
        """测试数据保存和加载"""
        # 生成一些测试数据
        self.simulator.collect_data()
        self.simulator.collect_data()
        
        original_count = len(self.simulator.realtime_data)
        original_data = self.simulator.realtime_data.copy()
        
        # 保存数据
        self.simulator.save_data()
        
        # 验证文件存在
        realtime_file = os.path.join(self.test_data_dir, self.simulator.realtime_file)
        kline_file = os.path.join(self.test_data_dir, self.simulator.kline_file)
        
        self.assertTrue(os.path.exists(realtime_file))
        self.assertTrue(os.path.exists(kline_file))
        
        # 创建新的模拟器并加载数据
        new_simulator = gold_futures_realtime_simple.GoldFuturesRealtimeSimulator()
        
        # 验证数据加载
        self.assertEqual(len(new_simulator.realtime_data), original_count)
        self.assertEqual(new_simulator.realtime_data, original_data)
    
    def test_kline_format_conversion(self):
        """测试K线格式转换"""
        # 生成测试数据
        self.simulator.collect_data()
        
        # 保存K线格式
        self.simulator.save_kline_format()
        
        # 验证K线文件
        kline_file = os.path.join(self.test_data_dir, self.simulator.kline_file)
        self.assertTrue(os.path.exists(kline_file))
        
        # 加载并验证K线数据
        with open(kline_file, 'r', encoding='utf-8') as f:
            kline_data = json.load(f)
        
        self.assertEqual(len(kline_data), len(self.simulator.realtime_data))
        
        # 验证K线数据格式
        if kline_data:
            kline_item = kline_data[0]
            self.assertEqual(len(kline_item), 6)  # [时间, 开盘, 收盘, 最低, 最高, 成交量]
            self.assertIsInstance(kline_item[0], str)  # 时间
            self.assertIsInstance(kline_item[1], (int, float))  # 开盘价
            self.assertIsInstance(kline_item[2], (int, float))  # 收盘价
            self.assertIsInstance(kline_item[3], (int, float))  # 最低价
            self.assertIsInstance(kline_item[4], (int, float))  # 最高价
            self.assertIsInstance(kline_item[5], int)  # 成交量
    
    def test_data_point_limit(self):
        """测试数据点数量限制"""
        # 设置较小的限制进行测试
        original_limit = self.simulator.max_data_points
        self.simulator.max_data_points = 5
        
        # 生成超过限制的数据
        for _ in range(10):
            self.simulator.collect_data()
        
        # 验证数据点数量不超过限制
        self.assertLessEqual(len(self.simulator.realtime_data), 5)
        
        # 恢复原始限制
        self.simulator.max_data_points = original_limit
    
    def test_status_reporting(self):
        """测试状态报告"""
        # 生成一些数据
        self.simulator.collect_data()
        
        status = self.simulator.get_status()
        
        # 验证状态信息
        self.assertIn('running', status)
        self.assertIn('data_points', status)
        self.assertIn('last_update', status)
        self.assertIn('current_price', status)
        self.assertIn('max_data_points', status)
        
        self.assertIsInstance(status['running'], bool)
        self.assertIsInstance(status['data_points'], int)
        self.assertIsInstance(status['current_price'], float)
        self.assertEqual(status['data_points'], len(self.simulator.realtime_data))
    
    def test_price_continuity(self):
        """测试价格连续性"""
        # 生成多个数据点
        prices = []
        for _ in range(5):
            self.simulator.collect_data()
            prices.append(self.simulator.current_price)
        
        # 验证价格变化合理性（相邻价格变化不应过大）
        for i in range(1, len(prices)):
            price_change_pct = abs(prices[i] - prices[i-1]) / prices[i-1]
            self.assertLess(price_change_pct, 0.05)  # 单次变化不超过5%
    
    def test_log_message(self):
        """测试日志记录"""
        test_message = "测试日志消息"
        
        # 记录日志
        self.simulator.log_message(test_message)
        
        # 验证日志文件
        log_file = os.path.join(self.test_data_dir, self.simulator.log_file)
        self.assertTrue(os.path.exists(log_file))
        
        # 验证日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
            self.assertIn(test_message, log_content)


class TestDataValidation(unittest.TestCase):
    """测试数据验证功能"""
    
    def test_price_range_validation(self):
        """测试价格范围验证"""
        simulator = gold_futures_realtime_simple.GoldFuturesRealtimeSimulator()
        
        # 生成多个数据点
        for _ in range(10):
            data_point = simulator.generate_realistic_price_data()
            
            # 验证价格在合理范围内
            self.assertGreater(data_point['price'], 0)
            self.assertLess(data_point['price'], 2000)  # 沪金价格不应超过2000元/克
            
            # 验证高低价关系
            self.assertGreaterEqual(data_point['high'], data_point['low'])
            self.assertGreaterEqual(data_point['high'], data_point['open'])
            self.assertLessEqual(data_point['low'], data_point['open'])
    
    def test_volume_validation(self):
        """测试成交量验证"""
        simulator = gold_futures_realtime_simple.GoldFuturesRealtimeSimulator()
        
        data_point = simulator.generate_realistic_price_data()
        
        # 验证成交量为正整数
        self.assertIsInstance(data_point['volume'], int)
        self.assertGreater(data_point['volume'], 0)
        self.assertIsInstance(data_point['open_interest'], int)
        self.assertGreater(data_point['open_interest'], 0)


def run_tests():
    """运行所有测试"""
    print("开始运行实时数据收集器单元测试...")
    print("="*60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestGoldFuturesRealtimeSimulator,
        TestDataValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
