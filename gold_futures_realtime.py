#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪金期货实时数据获取程序
每隔5分钟获取一次五分钟级别的实时行情数据

作者: 学习者
日期: 2025-08-05
"""

import akshare as ak
import pandas as pd
import datetime
import time
import os
import json
import schedule
import threading
from typing import Optional, List, Dict, Any


class GoldFuturesRealtimeCollector:
    """沪金期货实时数据收集器"""
    
    def __init__(self):
        self.data_dir = 'tempfiles'
        self.realtime_file = 'gold_futures_realtime_5min.json'
        self.log_file = 'gold_futures_realtime.log'
        self.max_data_points = 288  # 24小时 * 12个5分钟 = 288个数据点
        self.running = False
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化数据存储
        self.realtime_data = []
        self.load_existing_data()
    
    def log_message(self, message: str) -> None:
        """记录日志消息"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        
        # 写入日志文件
        try:
            log_path = os.path.join(self.data_dir, self.log_file)
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def load_existing_data(self) -> None:
        """加载已存在的数据"""
        try:
            file_path = os.path.join(self.data_dir, self.realtime_file)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.realtime_data = json.load(f)
                self.log_message(f"加载了 {len(self.realtime_data)} 条历史数据")
            else:
                self.realtime_data = []
                self.log_message("没有找到历史数据文件，从空数据开始")
        except Exception as e:
            self.log_message(f"加载历史数据失败: {e}")
            self.realtime_data = []
    
    def get_gold_futures_spot_data(self) -> Optional[Dict[str, Any]]:
        """获取沪金期货实时行情数据"""
        try:
            self.log_message("开始获取沪金期货实时行情数据...")
            
            # 获取上海期货交易所实时行情数据
            futures_spot_df = ak.futures_zh_spot(symbol="上海期货交易所")
            
            # 筛选沪金期货数据（代码包含 AU 的合约）
            gold_futures = futures_spot_df[futures_spot_df['代码'].str.contains('AU', na=False)]
            
            if gold_futures.empty:
                self.log_message("未找到沪金期货数据")
                return None
            
            # 获取主力合约数据（通常是成交量最大的合约）
            if '成交量' in gold_futures.columns and not gold_futures['成交量'].empty:
                # 找到成交量最大的合约
                max_volume_idx = gold_futures['成交量'].idxmax()
                main_contract = gold_futures.loc[max_volume_idx]
            else:
                # 如果没有成交量数据或为空，取第一个合约
                main_contract = gold_futures.iloc[0]
            
            # 安全获取数据的辅助函数
            def safe_get_float(value, default=0.0):
                try:
                    if pd.isna(value) or value == '' or value is None:
                        return default
                    return float(value)
                except (ValueError, TypeError):
                    return default

            def safe_get_int(value, default=0):
                try:
                    if pd.isna(value) or value == '' or value is None:
                        return default
                    return int(float(value))
                except (ValueError, TypeError):
                    return default

            def safe_get_str(value, default=''):
                try:
                    if pd.isna(value) or value is None:
                        return default
                    return str(value)
                except:
                    return default

            # 构造数据点
            data_point = {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'contract_code': safe_get_str(main_contract.get('代码', 'AU0000')),
                'contract_name': safe_get_str(main_contract.get('名称', '沪金主力')),
                'price': safe_get_float(main_contract.get('最新价', 0)),
                'change': safe_get_float(main_contract.get('涨跌', 0)),
                'change_pct': safe_get_float(main_contract.get('涨跌幅', 0)),
                'volume': safe_get_int(main_contract.get('成交量', 0)),
                'open_interest': safe_get_int(main_contract.get('持仓量', 0)),
                'open': safe_get_float(main_contract.get('今开', 0)),
                'high': safe_get_float(main_contract.get('最高', 0)),
                'low': safe_get_float(main_contract.get('最低', 0)),
                'pre_close': safe_get_float(main_contract.get('昨收', 0))
            }
            
            self.log_message(f"获取到数据: {data_point['contract_code']} 价格: {data_point['price']}")
            return data_point
            
        except Exception as e:
            self.log_message(f"获取实时数据失败: {e}")
            return None
    
    def get_gold_futures_minute_data(self) -> Optional[Dict[str, Any]]:
        """获取沪金期货分时数据（如果可用）"""
        try:
            # 注意：akshare的分时数据接口可能需要特定的symbol格式
            # 这里我们尝试获取AU主力合约的分时数据
            minute_data = ak.futures_zh_minute_sina(symbol="AU0", period="5")
            
            if minute_data.empty:
                return None
            
            # 获取最新的数据点
            latest = minute_data.iloc[-1]
            
            data_point = {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'datetime': latest.get('datetime', ''),
                'open': float(latest.get('open', 0)),
                'high': float(latest.get('high', 0)),
                'low': float(latest.get('low', 0)),
                'close': float(latest.get('close', 0)),
                'volume': int(latest.get('volume', 0))
            }
            
            return data_point
            
        except Exception as e:
            self.log_message(f"获取分时数据失败: {e}")
            return None
    
    def collect_data(self) -> None:
        """收集数据的主要方法"""
        try:
            # 首先尝试获取实时行情数据
            spot_data = self.get_gold_futures_spot_data()
            
            if spot_data:
                # 添加数据到列表
                self.realtime_data.append(spot_data)
                
                # 保持数据点数量在限制范围内
                if len(self.realtime_data) > self.max_data_points:
                    self.realtime_data = self.realtime_data[-self.max_data_points:]
                
                # 保存数据
                self.save_data()
                
                self.log_message(f"数据收集成功，当前共有 {len(self.realtime_data)} 个数据点")
            else:
                self.log_message("数据收集失败")
                
        except Exception as e:
            self.log_message(f"数据收集过程中出错: {e}")
    
    def save_data(self) -> None:
        """保存数据到文件"""
        try:
            file_path = os.path.join(self.data_dir, self.realtime_file)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.realtime_data, f, ensure_ascii=False, indent=2)
            
            # 同时保存为K线格式的数据
            self.save_kline_format()
            
        except Exception as e:
            self.log_message(f"保存数据失败: {e}")
    
    def save_kline_format(self) -> None:
        """将数据保存为K线格式"""
        try:
            if not self.realtime_data:
                return
            
            kline_data = []
            for item in self.realtime_data:
                # 构造K线数据格式: [时间, 开盘, 收盘, 最低, 最高, 成交量]
                kline_item = [
                    item['timestamp'],
                    item.get('open', item['price']),
                    item['price'],  # 使用当前价格作为收盘价
                    item.get('low', item['price']),
                    item.get('high', item['price']),
                    item['volume']
                ]
                kline_data.append(kline_item)
            
            # 保存K线格式数据
            kline_file = 'gold_futures_realtime_kline.json'
            file_path = os.path.join(self.data_dir, kline_file)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(kline_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.log_message(f"保存K线格式数据失败: {e}")
    
    def start_collection(self) -> None:
        """开始数据收集"""
        self.log_message("启动沪金期货实时数据收集器")
        self.running = True
        
        # 立即收集一次数据
        self.collect_data()
        
        # 设置定时任务，每5分钟执行一次
        schedule.every(5).minutes.do(self.collect_data)
        
        # 在单独的线程中运行调度器
        def run_scheduler():
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        self.log_message("数据收集器已启动，每5分钟收集一次数据")
    
    def stop_collection(self) -> None:
        """停止数据收集"""
        self.running = False
        schedule.clear()
        self.log_message("数据收集器已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取收集器状态"""
        return {
            'running': self.running,
            'data_points': len(self.realtime_data),
            'last_update': self.realtime_data[-1]['timestamp'] if self.realtime_data else None,
            'max_data_points': self.max_data_points
        }


def main():
    """主函数"""
    print("沪金期货实时数据收集器")
    print("="*50)
    
    collector = GoldFuturesRealtimeCollector()
    
    try:
        # 启动数据收集
        collector.start_collection()
        
        print("数据收集器正在运行...")
        print("按 Ctrl+C 停止收集")
        
        # 保持程序运行
        while True:
            time.sleep(10)
            status = collector.get_status()
            print(f"状态: 运行中, 数据点: {status['data_points']}, 最后更新: {status['last_update']}")
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        collector.stop_collection()
        print("数据收集器已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        collector.stop_collection()


if __name__ == "__main__":
    main()
