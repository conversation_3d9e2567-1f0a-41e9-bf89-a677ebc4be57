#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪金期货数据获取程序的单元测试

作者: 学习者
日期: 2025-08-05
"""

import unittest
import pandas as pd
import os
import sys
import json
from unittest.mock import patch, MagicMock

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的模块
import gold_futures_data


class TestGoldFuturesData(unittest.TestCase):
    """测试沪金期货数据获取功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.test_data_dir = 'tempfiles'
        if not os.path.exists(self.test_data_dir):
            os.makedirs(self.test_data_dir)
    
    def test_save_data_to_csv(self):
        """测试数据保存功能"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2025-08-01', '2025-08-02', '2025-08-03'],
            'open': [780.0, 785.0, 790.0],
            'high': [785.0, 790.0, 795.0],
            'low': [775.0, 780.0, 785.0],
            'close': [783.0, 788.0, 793.0],
            'volume': [10000, 12000, 11000]
        })
        
        # 测试保存功能
        filename = 'test_gold_data.csv'
        gold_futures_data.save_data_to_csv(test_data, filename)
        
        # 验证文件是否创建
        filepath = os.path.join(self.test_data_dir, filename)
        self.assertTrue(os.path.exists(filepath))
        
        # 验证数据是否正确保存
        loaded_data = pd.read_csv(filepath)
        self.assertEqual(len(loaded_data), 3)
        self.assertIn('date', loaded_data.columns)
        self.assertIn('open', loaded_data.columns)
        self.assertIn('close', loaded_data.columns)
        
        # 清理测试文件
        if os.path.exists(filepath):
            os.remove(filepath)
    
    def test_prepare_kline_data(self):
        """测试K线数据准备功能"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2025-08-01', '2025-08-02', '2025-08-03'],
            'open': [780.0, 785.0, 790.0],
            'high': [785.0, 790.0, 795.0],
            'low': [775.0, 780.0, 785.0],
            'close': [783.0, 788.0, 793.0],
            'volume': [10000, 12000, 11000]
        })
        
        # 测试K线数据准备
        kline_data = gold_futures_data.prepare_kline_data(test_data)
        
        # 验证结果
        self.assertEqual(len(kline_data), 3)
        self.assertEqual(len(kline_data[0]), 6)  # [日期, 开盘, 收盘, 最低, 最高, 成交量]
        
        # 验证第一条数据
        first_item = kline_data[0]
        self.assertEqual(first_item[0], '2025-08-01')  # 日期
        self.assertEqual(first_item[1], 780.0)  # 开盘价
        self.assertEqual(first_item[2], 783.0)  # 收盘价
        self.assertEqual(first_item[3], 775.0)  # 最低价
        self.assertEqual(first_item[4], 785.0)  # 最高价
        self.assertEqual(first_item[5], 10000)  # 成交量
    
    def test_prepare_kline_data_with_chinese_columns(self):
        """测试使用中文列名的K线数据准备功能"""
        # 创建使用中文列名的测试数据
        test_data = pd.DataFrame({
            '日期': ['2025-08-01', '2025-08-02', '2025-08-03'],
            '开盘价': [780.0, 785.0, 790.0],
            '最高价': [785.0, 790.0, 795.0],
            '最低价': [775.0, 780.0, 785.0],
            '收盘价': [783.0, 788.0, 793.0],
            '成交量': [10000, 12000, 11000]
        })
        
        # 测试K线数据准备
        kline_data = gold_futures_data.prepare_kline_data(test_data)
        
        # 验证结果
        self.assertEqual(len(kline_data), 3)
        self.assertEqual(len(kline_data[0]), 6)
        
        # 验证第一条数据
        first_item = kline_data[0]
        self.assertEqual(first_item[0], '2025-08-01')
        self.assertEqual(first_item[1], 780.0)
        self.assertEqual(first_item[2], 783.0)
    
    def test_prepare_kline_data_empty(self):
        """测试空数据的K线数据准备"""
        # 测试空DataFrame
        empty_data = pd.DataFrame()
        kline_data = gold_futures_data.prepare_kline_data(empty_data)
        self.assertEqual(len(kline_data), 0)
        
        # 测试None
        kline_data = gold_futures_data.prepare_kline_data(None)
        self.assertEqual(len(kline_data), 0)
    
    def test_save_kline_data_json(self):
        """测试K线数据JSON保存功能"""
        # 创建测试K线数据
        test_kline_data = [
            ['2025-08-01', 780.0, 783.0, 775.0, 785.0, 10000],
            ['2025-08-02', 785.0, 788.0, 780.0, 790.0, 12000],
            ['2025-08-03', 790.0, 793.0, 785.0, 795.0, 11000]
        ]
        
        # 测试保存功能
        filename = 'test_kline_data.json'
        gold_futures_data.save_kline_data_json(test_kline_data, filename)
        
        # 验证文件是否创建
        filepath = os.path.join(self.test_data_dir, filename)
        self.assertTrue(os.path.exists(filepath))
        
        # 验证数据是否正确保存
        with open(filepath, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        self.assertEqual(len(loaded_data), 3)
        self.assertEqual(loaded_data[0][0], '2025-08-01')
        self.assertEqual(loaded_data[0][1], 780.0)
        
        # 清理测试文件
        if os.path.exists(filepath):
            os.remove(filepath)
    
    def test_display_data_summary(self):
        """测试数据摘要显示功能"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2025-08-01', '2025-08-02', '2025-08-03'],
            'close': [780.0, 785.0, 790.0],
            'volume': [10000, 12000, 11000]
        })
        
        # 这个函数主要是打印输出，我们测试它不会抛出异常
        try:
            gold_futures_data.display_data_summary(test_data, "测试数据")
            test_passed = True
        except Exception:
            test_passed = False
        
        self.assertTrue(test_passed)
    
    def test_display_data_summary_empty_data(self):
        """测试空数据的摘要显示"""
        # 测试空数据
        try:
            gold_futures_data.display_data_summary(None, "空数据测试")
            gold_futures_data.display_data_summary(pd.DataFrame(), "空DataFrame测试")
            test_passed = True
        except Exception:
            test_passed = False
        
        self.assertTrue(test_passed)
    
    @patch('akshare.futures_zh_spot')
    def test_get_gold_futures_realtime_mock(self, mock_futures_spot):
        """使用模拟数据测试实时数据获取"""
        # 模拟 akshare 返回的数据
        mock_data = pd.DataFrame({
            '代码': ['AU2510', 'AU2511', 'AU2512'],
            '名称': ['沪金2510', '沪金2511', '沪金2512'],
            '最新价': [780.0, 785.0, 790.0],
            '涨跌': [5.0, -3.0, 8.0],
            '涨跌幅': [0.64, -0.38, 1.02]
        })
        mock_futures_spot.return_value = mock_data
        
        # 测试函数
        result = gold_futures_data.get_gold_futures_realtime()
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertTrue(all('AU' in code for code in result['代码']))
    
    @patch('akshare.futures_zh_daily_sina')
    def test_get_gold_futures_au2510_data_mock(self, mock_futures_daily):
        """使用模拟数据测试AU2510合约数据获取"""
        # 模拟 akshare 返回的数据
        mock_data = pd.DataFrame({
            'date': ['2025-08-01', '2025-08-02', '2025-08-03'],
            'open': [780.0, 785.0, 790.0],
            'high': [785.0, 790.0, 795.0],
            'low': [775.0, 780.0, 785.0],
            'close': [783.0, 788.0, 793.0],
            'volume': [10000, 12000, 11000],
            'hold': [50000, 52000, 51000],
            'settle': [782.0, 787.0, 792.0]
        })
        mock_futures_daily.return_value = mock_data
        
        # 测试函数
        result = gold_futures_data.get_gold_futures_au2510_data()
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertIn('date', result.columns)
        self.assertIn('close', result.columns)
    
    @patch('akshare.futures_main_sina')
    def test_get_gold_futures_historical_mock(self, mock_futures_main):
        """使用模拟数据测试历史数据获取"""
        # 模拟 akshare 返回的数据
        mock_data = pd.DataFrame({
            '日期': ['2025-08-01', '2025-08-02', '2025-08-03'],
            '开盘价': [780, 785, 790],
            '最高价': [785, 790, 795],
            '最低价': [775, 780, 785],
            '收盘价': [783, 788, 793],
            '成交量': [100000, 120000, 110000],
            '持仓量': [200000, 205000, 202000],
            '动态结算价': [782, 787, 792]
        })
        mock_futures_main.return_value = mock_data
        
        # 测试函数
        result = gold_futures_data.get_gold_futures_historical()
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertIn('日期', result.columns)
        self.assertIn('收盘价', result.columns)


class TestDataValidation(unittest.TestCase):
    """测试数据验证功能"""
    
    def test_data_types(self):
        """测试数据类型验证"""
        # 测试价格数据应该是数值类型
        test_data = pd.DataFrame({
            'price': [780.0, 785.0, 790.0],
            'volume': [10000, 12000, 11000]
        })
        
        self.assertTrue(pd.api.types.is_numeric_dtype(test_data['price']))
        self.assertTrue(pd.api.types.is_numeric_dtype(test_data['volume']))
    
    def test_data_range(self):
        """测试数据范围验证"""
        # 沪金期货价格应该在合理范围内
        test_prices = [780.0, 785.0, 790.0, 775.0, 795.0]
        
        # 价格应该都是正数
        self.assertTrue(all(price > 0 for price in test_prices))
        
        # 价格应该在合理范围内（假设 300-1000 元/克）
        self.assertTrue(all(300 <= price <= 1000 for price in test_prices))


def run_tests():
    """运行所有测试"""
    print("开始运行沪金期货程序单元测试...")
    print("="*60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestGoldFuturesData,
        TestDataValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
