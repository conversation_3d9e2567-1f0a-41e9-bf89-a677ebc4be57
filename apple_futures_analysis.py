#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
苹果期货数据分析程序
分析苹果期货的价格走势、成交量、持仓量等指标

作者: 学习者
日期: 2025-08-05
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def load_data():
    """
    加载苹果期货数据
    
    Returns:
        tuple: (历史数据, 具体合约数据)
    """
    try:
        # 加载历史数据
        hist_file = 'tempfiles/apple_futures_historical.csv'
        daily_file = 'tempfiles/apple_futures_AP2510_daily.csv'
        
        if os.path.exists(hist_file):
            hist_data = pd.read_csv(hist_file)
            hist_data['日期'] = pd.to_datetime(hist_data['日期'])
            print(f"成功加载历史数据: {len(hist_data)} 条记录")
        else:
            hist_data = None
            print("历史数据文件不存在")
        
        if os.path.exists(daily_file):
            daily_data = pd.read_csv(daily_file)
            daily_data['date'] = pd.to_datetime(daily_data['date'])
            print(f"成功加载AP2510合约数据: {len(daily_data)} 条记录")
        else:
            daily_data = None
            print("AP2510合约数据文件不存在")
            
        return hist_data, daily_data
        
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None, None


def calculate_technical_indicators(data, price_col='收盘价'):
    """
    计算技术指标
    
    Args:
        data (pd.DataFrame): 价格数据
        price_col (str): 价格列名
    
    Returns:
        pd.DataFrame: 包含技术指标的数据
    """
    df = data.copy()
    
    # 计算移动平均线
    df['MA5'] = df[price_col].rolling(window=5).mean()
    df['MA10'] = df[price_col].rolling(window=10).mean()
    df['MA20'] = df[price_col].rolling(window=20).mean()
    
    # 计算价格变化
    df['价格变化'] = df[price_col].diff()
    df['价格变化率'] = df[price_col].pct_change() * 100
    
    # 计算波动率（20日滚动标准差）
    df['波动率'] = df[price_col].rolling(window=20).std()
    
    return df


def analyze_price_trend(data, title="苹果期货价格走势分析"):
    """
    分析价格趋势
    
    Args:
        data (pd.DataFrame): 价格数据
        title (str): 图表标题
    """
    print(f"\n{title}")
    print("="*50)
    
    if data is None or data.empty:
        print("数据为空，无法进行分析")
        return
    
    # 确定价格列名
    price_col = '收盘价' if '收盘价' in data.columns else 'close'
    date_col = '日期' if '日期' in data.columns else 'date'
    
    # 基本统计信息
    current_price = data[price_col].iloc[-1]
    max_price = data[price_col].max()
    min_price = data[price_col].min()
    avg_price = data[price_col].mean()
    
    print(f"当前价格: {current_price:.2f} 元/吨")
    print(f"最高价格: {max_price:.2f} 元/吨")
    print(f"最低价格: {min_price:.2f} 元/吨")
    print(f"平均价格: {avg_price:.2f} 元/吨")
    
    # 价格变化分析
    if len(data) > 1:
        price_change = data[price_col].iloc[-1] - data[price_col].iloc[-2]
        price_change_pct = (price_change / data[price_col].iloc[-2]) * 100
        print(f"最新变化: {price_change:+.2f} 元/吨 ({price_change_pct:+.2f}%)")
    
    # 计算技术指标
    data_with_indicators = calculate_technical_indicators(data, price_col)
    
    # 创建价格走势图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 价格走势图
    ax1.plot(data_with_indicators[date_col], data_with_indicators[price_col], 
             label='收盘价', linewidth=2, color='blue')
    ax1.plot(data_with_indicators[date_col], data_with_indicators['MA5'], 
             label='MA5', alpha=0.7, color='red')
    ax1.plot(data_with_indicators[date_col], data_with_indicators['MA10'], 
             label='MA10', alpha=0.7, color='green')
    ax1.plot(data_with_indicators[date_col], data_with_indicators['MA20'], 
             label='MA20', alpha=0.7, color='orange')
    
    ax1.set_title(f'{title} - 价格走势', fontsize=14, fontweight='bold')
    ax1.set_ylabel('价格 (元/吨)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 价格变化率图
    if '价格变化率' in data_with_indicators.columns:
        ax2.bar(data_with_indicators[date_col], data_with_indicators['价格变化率'], 
                alpha=0.7, color=['red' if x < 0 else 'green' for x in data_with_indicators['价格变化率']])
        ax2.set_title('价格变化率', fontsize=14, fontweight='bold')
        ax2.set_ylabel('变化率 (%)', fontsize=12)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    chart_filename = f"tempfiles/{title.replace(' ', '_')}_chart.png"
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"价格走势图已保存到: {chart_filename}")
    
    plt.show()


def analyze_volume_and_position(data, title="成交量和持仓量分析"):
    """
    分析成交量和持仓量
    
    Args:
        data (pd.DataFrame): 数据
        title (str): 图表标题
    """
    print(f"\n{title}")
    print("="*50)
    
    if data is None or data.empty:
        print("数据为空，无法进行分析")
        return
    
    # 确定列名
    volume_col = '成交量' if '成交量' in data.columns else 'volume'
    position_col = '持仓量' if '持仓量' in data.columns else 'hold'
    date_col = '日期' if '日期' in data.columns else 'date'
    
    if volume_col not in data.columns or position_col not in data.columns:
        print("缺少成交量或持仓量数据")
        return
    
    # 基本统计
    avg_volume = data[volume_col].mean()
    max_volume = data[volume_col].max()
    avg_position = data[position_col].mean()
    max_position = data[position_col].max()
    
    print(f"平均成交量: {avg_volume:,.0f} 手")
    print(f"最大成交量: {max_volume:,.0f} 手")
    print(f"平均持仓量: {avg_position:,.0f} 手")
    print(f"最大持仓量: {max_position:,.0f} 手")
    
    # 创建成交量和持仓量图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 成交量图
    ax1.bar(data[date_col], data[volume_col], alpha=0.7, color='skyblue')
    ax1.set_title(f'{title} - 成交量', fontsize=14, fontweight='bold')
    ax1.set_ylabel('成交量 (手)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 持仓量图
    ax2.plot(data[date_col], data[position_col], color='orange', linewidth=2)
    ax2.fill_between(data[date_col], data[position_col], alpha=0.3, color='orange')
    ax2.set_title('持仓量', fontsize=14, fontweight='bold')
    ax2.set_ylabel('持仓量 (手)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    chart_filename = f"tempfiles/{title.replace(' ', '_')}_chart.png"
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"成交量持仓量图已保存到: {chart_filename}")
    
    plt.show()


def generate_summary_report(hist_data, daily_data):
    """
    生成分析报告
    
    Args:
        hist_data (pd.DataFrame): 历史数据
        daily_data (pd.DataFrame): 日线数据
    """
    print("\n" + "="*60)
    print("苹果期货数据分析报告")
    print("="*60)
    
    report_lines = []
    report_lines.append("苹果期货数据分析报告")
    report_lines.append("="*60)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    if hist_data is not None:
        report_lines.append("1. 主力连续合约分析")
        report_lines.append("-" * 30)
        current_price = hist_data['收盘价'].iloc[-1]
        max_price = hist_data['收盘价'].max()
        min_price = hist_data['收盘价'].min()
        avg_volume = hist_data['成交量'].mean()
        
        report_lines.append(f"数据时间范围: {hist_data['日期'].min().strftime('%Y-%m-%d')} 至 {hist_data['日期'].max().strftime('%Y-%m-%d')}")
        report_lines.append(f"当前价格: {current_price:.2f} 元/吨")
        report_lines.append(f"期间最高价: {max_price:.2f} 元/吨")
        report_lines.append(f"期间最低价: {min_price:.2f} 元/吨")
        report_lines.append(f"平均成交量: {avg_volume:,.0f} 手")
        report_lines.append("")
    
    if daily_data is not None:
        report_lines.append("2. AP2510合约分析")
        report_lines.append("-" * 30)
        current_price = daily_data['close'].iloc[-1]
        max_price = daily_data['close'].max()
        min_price = daily_data['close'].min()
        avg_volume = daily_data['volume'].mean()
        
        report_lines.append(f"数据时间范围: {daily_data['date'].min().strftime('%Y-%m-%d')} 至 {daily_data['date'].max().strftime('%Y-%m-%d')}")
        report_lines.append(f"当前价格: {current_price:.2f} 元/吨")
        report_lines.append(f"期间最高价: {max_price:.2f} 元/吨")
        report_lines.append(f"期间最低价: {min_price:.2f} 元/吨")
        report_lines.append(f"平均成交量: {avg_volume:,.0f} 手")
        report_lines.append("")
    
    report_lines.append("3. 投资建议")
    report_lines.append("-" * 30)
    report_lines.append("• 建议关注苹果期货的季节性特征")
    report_lines.append("• 注意成交量变化，高成交量通常伴随价格突破")
    report_lines.append("• 关注持仓量变化，了解市场参与度")
    report_lines.append("• 建议结合现货价格和基本面分析")
    report_lines.append("")
    
    # 打印报告
    for line in report_lines:
        print(line)
    
    # 保存报告
    report_filename = "tempfiles/apple_futures_analysis_report.txt"
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"分析报告已保存到: {report_filename}")


def main():
    """
    主函数：执行苹果期货数据分析
    """
    print("苹果期货数据分析程序启动")
    print("="*60)
    
    # 加载数据
    hist_data, daily_data = load_data()
    
    # 分析历史数据
    if hist_data is not None:
        analyze_price_trend(hist_data, "苹果期货主力连续合约")
        analyze_volume_and_position(hist_data, "主力连续合约成交量持仓量")
    
    # 分析具体合约数据
    if daily_data is not None:
        analyze_price_trend(daily_data, "苹果期货AP2510合约")
        analyze_volume_and_position(daily_data, "AP2510合约成交量持仓量")
    
    # 生成综合报告
    generate_summary_report(hist_data, daily_data)
    
    print("\n" + "="*60)
    print("数据分析完成！所有图表和报告已保存到 tempfiles 目录")
    print("="*60)


if __name__ == "__main__":
    main()
