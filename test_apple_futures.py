#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
苹果期货数据获取和分析程序的单元测试

作者: 学习者
日期: 2025-08-05
"""

import unittest
import pandas as pd
import os
import sys
from unittest.mock import patch, MagicMock
from datetime import datetime

# 添加当前目录到 Python 路径，以便导入我们的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入我们要测试的模块
import apple_futures_data
import apple_futures_analysis


class TestAppleFuturesData(unittest.TestCase):
    """测试苹果期货数据获取功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.test_data_dir = 'tempfiles'
        if not os.path.exists(self.test_data_dir):
            os.makedirs(self.test_data_dir)
    
    def test_save_data_to_csv(self):
        """测试数据保存功能"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2025-08-01', '2025-08-02', '2025-08-03'],
            'price': [7800, 7850, 7900],
            'volume': [1000, 1200, 1100]
        })
        
        # 测试保存功能
        filename = 'test_data.csv'
        apple_futures_data.save_data_to_csv(test_data, filename)
        
        # 验证文件是否创建
        filepath = os.path.join(self.test_data_dir, filename)
        self.assertTrue(os.path.exists(filepath))
        
        # 验证数据是否正确保存
        loaded_data = pd.read_csv(filepath)
        self.assertEqual(len(loaded_data), 3)
        self.assertIn('date', loaded_data.columns)
        self.assertIn('price', loaded_data.columns)
        self.assertIn('volume', loaded_data.columns)
        
        # 清理测试文件
        if os.path.exists(filepath):
            os.remove(filepath)
    
    def test_display_data_summary(self):
        """测试数据摘要显示功能"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'price': [7800, 7850, 7900, 7750, 7820],
            'volume': [1000, 1200, 1100, 900, 1050]
        })
        
        # 这个函数主要是打印输出，我们测试它不会抛出异常
        try:
            apple_futures_data.display_data_summary(test_data, "测试数据")
            test_passed = True
        except Exception:
            test_passed = False
        
        self.assertTrue(test_passed)
    
    def test_display_data_summary_empty_data(self):
        """测试空数据的摘要显示"""
        # 测试空数据
        try:
            apple_futures_data.display_data_summary(None, "空数据测试")
            apple_futures_data.display_data_summary(pd.DataFrame(), "空DataFrame测试")
            test_passed = True
        except Exception:
            test_passed = False
        
        self.assertTrue(test_passed)
    
    @patch('akshare.futures_zh_spot')
    def test_get_apple_futures_realtime_mock(self, mock_futures_spot):
        """使用模拟数据测试实时数据获取"""
        # 模拟 akshare 返回的数据
        mock_data = pd.DataFrame({
            '代码': ['AP2510', 'AP2511', 'AP2512'],
            '名称': ['苹果2510', '苹果2511', '苹果2512'],
            '最新价': [7800, 7850, 7900],
            '涨跌': [10, -20, 30],
            '涨跌幅': [0.13, -0.26, 0.34]
        })
        mock_futures_spot.return_value = mock_data
        
        # 测试函数
        result = apple_futures_data.get_apple_futures_realtime()
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertTrue(all('AP' in code for code in result['代码']))
    
    @patch('akshare.futures_main_sina')
    def test_get_apple_futures_historical_mock(self, mock_futures_main):
        """使用模拟数据测试历史数据获取"""
        # 模拟 akshare 返回的数据
        mock_data = pd.DataFrame({
            '日期': ['2025-08-01', '2025-08-02', '2025-08-03'],
            '开盘价': [7800, 7850, 7820],
            '最高价': [7850, 7900, 7880],
            '最低价': [7780, 7830, 7800],
            '收盘价': [7830, 7870, 7850],
            '成交量': [50000, 60000, 55000],
            '持仓量': [100000, 105000, 102000],
            '动态结算价': [7835, 7875, 7855]
        })
        mock_futures_main.return_value = mock_data
        
        # 测试函数
        result = apple_futures_data.get_apple_futures_historical("AP0", "20250801", "20250803")
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertIn('日期', result.columns)
        self.assertIn('收盘价', result.columns)


class TestAppleFuturesAnalysis(unittest.TestCase):
    """测试苹果期货数据分析功能"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建测试数据
        self.test_hist_data = pd.DataFrame({
            '日期': pd.date_range('2025-08-01', periods=10, freq='D'),
            '开盘价': [7800, 7850, 7820, 7880, 7900, 7850, 7870, 7890, 7860, 7880],
            '最高价': [7850, 7900, 7880, 7920, 7950, 7900, 7920, 7940, 7910, 7930],
            '最低价': [7780, 7830, 7800, 7860, 7880, 7830, 7850, 7870, 7840, 7860],
            '收盘价': [7830, 7870, 7850, 7900, 7920, 7880, 7900, 7920, 7890, 7910],
            '成交量': [50000, 60000, 55000, 70000, 80000, 65000, 75000, 85000, 60000, 70000],
            '持仓量': [100000, 105000, 102000, 108000, 110000, 107000, 109000, 112000, 108000, 110000],
            '动态结算价': [7835, 7875, 7855, 7905, 7925, 7885, 7905, 7925, 7895, 7915]
        })
        
        self.test_daily_data = pd.DataFrame({
            'date': pd.date_range('2025-08-01', periods=10, freq='D'),
            'open': [7800, 7850, 7820, 7880, 7900, 7850, 7870, 7890, 7860, 7880],
            'high': [7850, 7900, 7880, 7920, 7950, 7900, 7920, 7940, 7910, 7930],
            'low': [7780, 7830, 7800, 7860, 7880, 7830, 7850, 7870, 7840, 7860],
            'close': [7830, 7870, 7850, 7900, 7920, 7880, 7900, 7920, 7890, 7910],
            'volume': [50000, 60000, 55000, 70000, 80000, 65000, 75000, 85000, 60000, 70000],
            'hold': [100000, 105000, 102000, 108000, 110000, 107000, 109000, 112000, 108000, 110000],
            'settle': [7835, 7875, 7855, 7905, 7925, 7885, 7905, 7925, 7895, 7915]
        })
    
    def test_calculate_technical_indicators(self):
        """测试技术指标计算"""
        result = apple_futures_analysis.calculate_technical_indicators(self.test_hist_data, '收盘价')
        
        # 验证新增的技术指标列
        self.assertIn('MA5', result.columns)
        self.assertIn('MA10', result.columns)
        self.assertIn('MA20', result.columns)
        self.assertIn('价格变化', result.columns)
        self.assertIn('价格变化率', result.columns)
        self.assertIn('波动率', result.columns)
        
        # 验证移动平均线计算
        # MA5 应该是前5个收盘价的平均值
        expected_ma5_5th = self.test_hist_data['收盘价'].iloc[:5].mean()
        actual_ma5_5th = result['MA5'].iloc[4]
        self.assertAlmostEqual(expected_ma5_5th, actual_ma5_5th, places=2)
        
        # 验证价格变化计算
        expected_change = self.test_hist_data['收盘价'].iloc[1] - self.test_hist_data['收盘价'].iloc[0]
        actual_change = result['价格变化'].iloc[1]
        self.assertEqual(expected_change, actual_change)
    
    def test_analyze_price_trend(self):
        """测试价格趋势分析"""
        # 这个函数主要是分析和绘图，我们测试它不会抛出异常
        try:
            # 由于绘图可能在无头环境中失败，我们使用 Agg 后端
            import matplotlib
            matplotlib.use('Agg')
            
            apple_futures_analysis.analyze_price_trend(self.test_hist_data, "测试价格趋势")
            test_passed = True
        except Exception as e:
            print(f"价格趋势分析测试失败: {e}")
            test_passed = False
        
        self.assertTrue(test_passed)
    
    def test_analyze_volume_and_position(self):
        """测试成交量和持仓量分析"""
        try:
            # 使用 Agg 后端避免显示问题
            import matplotlib
            matplotlib.use('Agg')
            
            apple_futures_analysis.analyze_volume_and_position(self.test_hist_data, "测试成交量持仓量")
            test_passed = True
        except Exception as e:
            print(f"成交量持仓量分析测试失败: {e}")
            test_passed = False
        
        self.assertTrue(test_passed)
    
    def test_generate_summary_report(self):
        """测试分析报告生成"""
        try:
            apple_futures_analysis.generate_summary_report(self.test_hist_data, self.test_daily_data)
            test_passed = True
        except Exception as e:
            print(f"报告生成测试失败: {e}")
            test_passed = False
        
        self.assertTrue(test_passed)
    
    def test_load_data_file_not_exist(self):
        """测试加载不存在的数据文件"""
        # 临时重命名文件（如果存在）
        hist_file = 'tempfiles/apple_futures_historical.csv'
        daily_file = 'tempfiles/apple_futures_AP2510_daily.csv'
        
        hist_backup = hist_file + '.backup' if os.path.exists(hist_file) else None
        daily_backup = daily_file + '.backup' if os.path.exists(daily_file) else None
        
        if hist_backup:
            os.rename(hist_file, hist_backup)
        if daily_backup:
            os.rename(daily_file, daily_backup)
        
        try:
            hist_data, daily_data = apple_futures_analysis.load_data()
            # 当文件不存在时，应该返回 None
            self.assertIsNone(hist_data)
            self.assertIsNone(daily_data)
        finally:
            # 恢复文件
            if hist_backup:
                os.rename(hist_backup, hist_file)
            if daily_backup:
                os.rename(daily_backup, daily_file)


class TestDataValidation(unittest.TestCase):
    """测试数据验证功能"""
    
    def test_data_types(self):
        """测试数据类型验证"""
        # 测试价格数据应该是数值类型
        test_data = pd.DataFrame({
            'price': [7800, 7850, 7900],
            'volume': [1000, 1200, 1100]
        })
        
        self.assertTrue(pd.api.types.is_numeric_dtype(test_data['price']))
        self.assertTrue(pd.api.types.is_numeric_dtype(test_data['volume']))
    
    def test_data_range(self):
        """测试数据范围验证"""
        # 苹果期货价格应该在合理范围内
        test_prices = [7800, 7850, 7900, 7750, 7820]
        
        # 价格应该都是正数
        self.assertTrue(all(price > 0 for price in test_prices))
        
        # 价格应该在合理范围内（假设 5000-15000 元/吨）
        self.assertTrue(all(5000 <= price <= 15000 for price in test_prices))


def run_tests():
    """运行所有测试"""
    print("开始运行苹果期货程序单元测试...")
    print("="*60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestAppleFuturesData,
        TestAppleFuturesAnalysis,
        TestDataValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
