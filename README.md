# Demo Qihuo 项目

这是一个 Python 项目，使用虚拟环境进行依赖管理。

## 项目结构

```
demo-qihuo/
├── venv/                        # Python 虚拟环境目录
├── tempfiles/                   # 临时文件存储目录
│   ├── apple_futures_historical.csv           # 苹果期货历史数据
│   ├── apple_futures_AP2510_daily.csv        # AP2510合约日线数据
│   ├── apple_futures_analysis_report.txt     # 数据分析报告
│   ├── gold_futures_AU2510.csv               # 沪金AU2510合约数据
│   ├── gold_futures_historical.csv           # 沪金主力连续合约数据
│   ├── gold_futures_AU2510_kline.json        # 沪金AU2510 K线数据
│   ├── gold_futures_historical_kline.json    # 沪金主力连续K线数据
│   └── *.png                                  # 生成的图表文件
├── apple_futures_data.py        # 苹果期货数据获取程序
├── apple_futures_analysis.py    # 苹果期货数据分析程序
├── test_apple_futures.py        # 苹果期货单元测试程序
├── gold_futures_data.py         # 沪金期货数据获取程序
├── gold_futures_kline.html      # 沪金期货K线图HTML页面
├── gold_futures_realtime_simple.py  # 实时数据收集器（模拟模式）
├── start_realtime_collector.py  # 实时数据收集器启动脚本
├── test_gold_futures.py         # 沪金期货单元测试程序
├── test_realtime_collector.py   # 实时数据收集器单元测试
├── requirements.txt             # Python 依赖包列表
├── .gitignore                   # Git 忽略文件配置
├── README.md                    # 项目说明文档
└── CHANGELOG.md                 # 项目变更日志
```

## 环境设置

### 1. 激活虚拟环境

在 macOS/Linux 系统中：
```bash
source venv/bin/activate
```

在 Windows 系统中：
```bash
venv\Scripts\activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 退出虚拟环境

```bash
deactivate
```

## 功能特性

### 数据获取功能
- **实时行情数据**：获取苹果期货各合约的实时价格、涨跌幅等信息
- **历史数据**：获取苹果期货主力连续合约的历史价格数据
- **具体合约数据**：获取指定合约（如AP2510）的详细日线数据

### 数据分析功能
- **技术指标计算**：移动平均线（MA5、MA10、MA20）、价格变化率、波动率
- **价格走势分析**：生成价格走势图表，包含技术指标
- **成交量分析**：分析成交量和持仓量变化趋势
- **数据可视化**：生成专业的图表和报告

### 程序文件说明

**苹果期货模块：**
- `apple_futures_data.py`：苹果期货数据获取主程序，使用akshare库获取期货数据
- `apple_futures_analysis.py`：苹果期货数据分析主程序，进行技术分析和可视化
- `test_apple_futures.py`：苹果期货完整的单元测试套件，确保代码质量

**沪金期货模块：**
- `gold_futures_data.py`：沪金期货数据获取主程序，获取AU2510合约和主力连续合约数据
- `gold_futures_kline.html`：交互式K线图HTML页面，支持多数据源和技术指标
- `test_gold_futures.py`：沪金期货完整的单元测试套件，确保代码质量

## 使用方法

### 苹果期货模块

#### 1. 获取苹果期货数据
```bash
python apple_futures_data.py
```

#### 2. 分析苹果期货数据
```bash
python apple_futures_analysis.py
```

#### 3. 运行苹果期货单元测试
```bash
python test_apple_futures.py
```

### 沪金期货模块

#### 1. 获取沪金期货数据
```bash
python gold_futures_data.py
```

#### 2. 查看沪金期货K线图
在浏览器中打开 `gold_futures_kline.html` 文件，即可查看交互式K线图。

功能特性：
- 支持AU2510合约和主力连续合约数据切换
- 提供时间范围筛选（全部/最近30天/60天/90天）
- 集成技术指标：MA5、MA10、MA20移动平均线
- 显示成交量柱状图
- 支持数据缩放和拖拽操作
- 响应式设计，支持移动端访问

#### 3. 启动实时数据收集器
```bash
python start_realtime_collector.py
```

这将启动一个后台服务，每5分钟自动收集一次沪金期货数据。

#### 4. 运行沪金期货单元测试
```bash
python test_gold_futures.py
python test_realtime_collector.py
```

## 开发指南

1. 在开始开发前，请确保激活虚拟环境
2. 安装新的包时，请更新 `requirements.txt` 文件
3. 所有临时文件请存放在 `tempfiles/` 目录中
4. 提交代码前请确保所有测试通过
5. 每个新功能都应该编写相应的单元测试

## 注意事项

- 虚拟环境目录 `venv/` 已被添加到 `.gitignore` 中，不会被提交到版本控制
- 请定期更新 `CHANGELOG.md` 记录项目变更
