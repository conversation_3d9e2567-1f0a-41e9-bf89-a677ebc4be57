# Demo Qihuo 项目

这是一个 Python 项目，使用虚拟环境进行依赖管理。

## 项目结构

```
demo-qihuo/
├── venv/                        # Python 虚拟环境目录
├── tempfiles/                   # 临时文件存储目录
│   ├── apple_futures_historical.csv           # 苹果期货历史数据
│   ├── apple_futures_AP2510_daily.csv        # AP2510合约日线数据
│   ├── apple_futures_analysis_report.txt     # 数据分析报告
│   └── *.png                                  # 生成的图表文件
├── apple_futures_data.py        # 苹果期货数据获取程序
├── apple_futures_analysis.py    # 苹果期货数据分析程序
├── test_apple_futures.py        # 单元测试程序
├── requirements.txt             # Python 依赖包列表
├── .gitignore                   # Git 忽略文件配置
├── README.md                    # 项目说明文档
└── CHANGELOG.md                 # 项目变更日志
```

## 环境设置

### 1. 激活虚拟环境

在 macOS/Linux 系统中：
```bash
source venv/bin/activate
```

在 Windows 系统中：
```bash
venv\Scripts\activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 退出虚拟环境

```bash
deactivate
```

## 功能特性

### 数据获取功能
- **实时行情数据**：获取苹果期货各合约的实时价格、涨跌幅等信息
- **历史数据**：获取苹果期货主力连续合约的历史价格数据
- **具体合约数据**：获取指定合约（如AP2510）的详细日线数据

### 数据分析功能
- **技术指标计算**：移动平均线（MA5、MA10、MA20）、价格变化率、波动率
- **价格走势分析**：生成价格走势图表，包含技术指标
- **成交量分析**：分析成交量和持仓量变化趋势
- **数据可视化**：生成专业的图表和报告

### 程序文件说明
- `apple_futures_data.py`：数据获取主程序，使用akshare库获取期货数据
- `apple_futures_analysis.py`：数据分析主程序，进行技术分析和可视化
- `test_apple_futures.py`：完整的单元测试套件，确保代码质量

## 使用方法

### 1. 获取苹果期货数据
```bash
python apple_futures_data.py
```

### 2. 分析苹果期货数据
```bash
python apple_futures_analysis.py
```

### 3. 运行单元测试
```bash
python test_apple_futures.py
```

## 开发指南

1. 在开始开发前，请确保激活虚拟环境
2. 安装新的包时，请更新 `requirements.txt` 文件
3. 所有临时文件请存放在 `tempfiles/` 目录中
4. 提交代码前请确保所有测试通过
5. 每个新功能都应该编写相应的单元测试

## 注意事项

- 虚拟环境目录 `venv/` 已被添加到 `.gitignore` 中，不会被提交到版本控制
- 请定期更新 `CHANGELOG.md` 记录项目变更
