# Demo Qihuo 项目

这是一个 Python 项目，使用虚拟环境进行依赖管理。

## 项目结构

```
demo-qihuo/
├── venv/                 # Python 虚拟环境目录
├── tempfiles/           # 临时文件存储目录
├── requirements.txt     # Python 依赖包列表
├── .gitignore          # Git 忽略文件配置
├── README.md           # 项目说明文档
└── CHANGELOG.md        # 项目变更日志
```

## 环境设置

### 1. 激活虚拟环境

在 macOS/Linux 系统中：
```bash
source venv/bin/activate
```

在 Windows 系统中：
```bash
venv\Scripts\activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 退出虚拟环境

```bash
deactivate
```

## 开发指南

1. 在开始开发前，请确保激活虚拟环境
2. 安装新的包时，请更新 `requirements.txt` 文件
3. 所有临时文件请存放在 `tempfiles/` 目录中
4. 提交代码前请确保所有测试通过

## 注意事项

- 虚拟环境目录 `venv/` 已被添加到 `.gitignore` 中，不会被提交到版本控制
- 请定期更新 `CHANGELOG.md` 记录项目变更
