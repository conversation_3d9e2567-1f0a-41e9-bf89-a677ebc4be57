#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪金期货实时数据获取程序（简化版）
每隔5分钟获取一次数据，使用模拟数据进行演示

作者: 学习者
日期: 2025-08-05
"""

import pandas as pd
import datetime
import time
import os
import json
import schedule
import threading
import random
from typing import Optional, List, Dict, Any


class GoldFuturesRealtimeSimulator:
    """沪金期货实时数据模拟器"""
    
    def __init__(self):
        self.data_dir = 'tempfiles'
        self.realtime_file = 'gold_futures_realtime_5min.json'
        self.kline_file = 'gold_futures_realtime_kline.json'
        self.log_file = 'gold_futures_realtime.log'
        self.max_data_points = 288  # 24小时 * 12个5分钟 = 288个数据点
        self.running = False
        
        # 基础价格（模拟当前沪金价格）
        self.base_price = 782.50
        self.current_price = self.base_price
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化数据存储
        self.realtime_data = []
        self.load_existing_data()
    
    def log_message(self, message: str) -> None:
        """记录日志消息"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        
        # 写入日志文件
        try:
            log_path = os.path.join(self.data_dir, self.log_file)
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def load_existing_data(self) -> None:
        """加载已存在的数据"""
        try:
            file_path = os.path.join(self.data_dir, self.realtime_file)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.realtime_data = json.load(f)
                
                # 从最后一个数据点恢复当前价格
                if self.realtime_data:
                    self.current_price = self.realtime_data[-1]['price']
                
                self.log_message(f"加载了 {len(self.realtime_data)} 条历史数据")
            else:
                self.realtime_data = []
                self.log_message("没有找到历史数据文件，从空数据开始")
        except Exception as e:
            self.log_message(f"加载历史数据失败: {e}")
            self.realtime_data = []
    
    def generate_realistic_price_data(self) -> Dict[str, Any]:
        """生成真实的价格数据"""
        try:
            # 模拟价格波动（-2% 到 +2%）
            price_change_pct = random.uniform(-0.02, 0.02)
            price_change = self.current_price * price_change_pct
            
            # 更新当前价格
            self.current_price += price_change
            
            # 确保价格在合理范围内
            min_price = self.base_price * 0.9  # 不低于基础价格的90%
            max_price = self.base_price * 1.1  # 不高于基础价格的110%
            self.current_price = max(min_price, min(max_price, self.current_price))
            
            # 生成开高低收数据
            volatility = random.uniform(0.001, 0.005)  # 0.1% - 0.5% 的波动
            high = self.current_price * (1 + volatility)
            low = self.current_price * (1 - volatility)
            open_price = random.uniform(low, high)
            
            # 生成成交量（模拟真实的成交量范围）
            base_volume = random.randint(10000, 50000)
            volume_multiplier = random.uniform(0.5, 2.0)
            volume = int(base_volume * volume_multiplier)
            
            # 构造数据点
            data_point = {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'contract_code': 'AU2510',
                'contract_name': '沪金2510',
                'price': round(self.current_price, 2),
                'change': round(price_change, 2),
                'change_pct': round(price_change_pct * 100, 2),
                'volume': volume,
                'open_interest': random.randint(80000, 120000),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'pre_close': round(self.current_price - price_change, 2)
            }
            
            self.log_message(f"生成数据: {data_point['contract_code']} 价格: {data_point['price']} 变化: {data_point['change']:+.2f}")
            return data_point
            
        except Exception as e:
            self.log_message(f"生成数据失败: {e}")
            return None
    
    def collect_data(self) -> None:
        """收集数据的主要方法"""
        try:
            # 生成模拟数据
            data_point = self.generate_realistic_price_data()
            
            if data_point:
                # 添加数据到列表
                self.realtime_data.append(data_point)
                
                # 保持数据点数量在限制范围内
                if len(self.realtime_data) > self.max_data_points:
                    self.realtime_data = self.realtime_data[-self.max_data_points:]
                
                # 保存数据
                self.save_data()
                
                self.log_message(f"数据收集成功，当前共有 {len(self.realtime_data)} 个数据点")
            else:
                self.log_message("数据收集失败")
                
        except Exception as e:
            self.log_message(f"数据收集过程中出错: {e}")
    
    def save_data(self) -> None:
        """保存数据到文件"""
        try:
            # 保存原始数据
            file_path = os.path.join(self.data_dir, self.realtime_file)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.realtime_data, f, ensure_ascii=False, indent=2)
            
            # 保存K线格式数据
            self.save_kline_format()
            
        except Exception as e:
            self.log_message(f"保存数据失败: {e}")
    
    def save_kline_format(self) -> None:
        """将数据保存为K线格式"""
        try:
            if not self.realtime_data:
                return
            
            kline_data = []
            for item in self.realtime_data:
                # 构造K线数据格式: [时间, 开盘, 收盘, 最低, 最高, 成交量]
                kline_item = [
                    item['timestamp'],
                    item['open'],
                    item['price'],  # 使用当前价格作为收盘价
                    item['low'],
                    item['high'],
                    item['volume']
                ]
                kline_data.append(kline_item)
            
            # 保存K线格式数据
            file_path = os.path.join(self.data_dir, self.kline_file)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(kline_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.log_message(f"保存K线格式数据失败: {e}")
    
    def start_collection(self) -> None:
        """开始数据收集"""
        self.log_message("启动沪金期货实时数据收集器（模拟模式）")
        self.running = True
        
        # 立即收集一次数据
        self.collect_data()
        
        # 设置定时任务，每5分钟执行一次
        schedule.every(5).minutes.do(self.collect_data)
        
        # 在单独的线程中运行调度器
        def run_scheduler():
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        self.log_message("数据收集器已启动，每5分钟收集一次数据")
    
    def stop_collection(self) -> None:
        """停止数据收集"""
        self.running = False
        schedule.clear()
        self.log_message("数据收集器已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取收集器状态"""
        return {
            'running': self.running,
            'data_points': len(self.realtime_data),
            'last_update': self.realtime_data[-1]['timestamp'] if self.realtime_data else None,
            'current_price': self.current_price,
            'max_data_points': self.max_data_points
        }


def main():
    """主函数"""
    print("沪金期货实时数据收集器（模拟模式）")
    print("="*50)
    
    simulator = GoldFuturesRealtimeSimulator()
    
    try:
        # 启动数据收集
        simulator.start_collection()
        
        print("数据收集器正在运行...")
        print("按 Ctrl+C 停止收集")
        
        # 保持程序运行
        while True:
            time.sleep(10)
            status = simulator.get_status()
            print(f"状态: 运行中, 数据点: {status['data_points']}, 当前价格: {status['current_price']:.2f}, 最后更新: {status['last_update']}")
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        simulator.stop_collection()
        print("数据收集器已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        simulator.stop_collection()


if __name__ == "__main__":
    main()
