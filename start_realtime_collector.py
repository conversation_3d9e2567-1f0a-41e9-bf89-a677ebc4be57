#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动沪金期货实时数据收集器

作者: 学习者
日期: 2025-08-05
"""

import gold_futures_realtime_simple
import time
import signal
import sys


def signal_handler(sig, frame):
    """处理中断信号"""
    print('\n收到停止信号，正在关闭数据收集器...')
    if 'collector' in globals():
        collector.stop_collection()
    print('数据收集器已停止')
    sys.exit(0)


def main():
    """主函数"""
    global collector
    
    print("="*60)
    print("沪金期货实时数据收集器")
    print("="*60)
    print("功能说明:")
    print("- 每5分钟自动收集一次沪金期货数据")
    print("- 生成实时K线数据供HTML页面使用")
    print("- 数据保存在 tempfiles 目录中")
    print("- 按 Ctrl+C 可以停止收集器")
    print("="*60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建数据收集器
    collector = gold_futures_realtime_simple.GoldFuturesRealtimeSimulator()
    
    try:
        # 启动数据收集
        collector.start_collection()
        
        print("✅ 数据收集器已启动")
        print("📊 每5分钟自动收集一次数据")
        print("🌐 HTML页面将每5分钟自动刷新")
        print("📁 数据文件: tempfiles/gold_futures_realtime_kline.json")
        print("📝 日志文件: tempfiles/gold_futures_realtime.log")
        print("\n状态监控:")
        print("-" * 60)
        
        # 保持程序运行并显示状态
        while True:
            time.sleep(30)  # 每30秒显示一次状态
            status = collector.get_status()
            
            print(f"⏰ {time.strftime('%Y-%m-%d %H:%M:%S')} | "
                  f"数据点: {status['data_points']:3d} | "
                  f"当前价格: {status['current_price']:7.2f} 元/克 | "
                  f"最后更新: {status['last_update']}")
            
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        collector.stop_collection()
        sys.exit(1)


if __name__ == "__main__":
    main()
