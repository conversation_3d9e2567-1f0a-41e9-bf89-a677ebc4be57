# 更新日志

本文件记录项目的所有重要变更。

## [未发布]

### 新增
- 安装并配置了 akshare 数据获取库
- 创建了苹果期货数据获取程序 (apple_futures_data.py)
- 创建了苹果期货数据分析程序 (apple_futures_analysis.py)
- 实现了苹果期货实时行情数据获取功能
- 实现了苹果期货历史数据获取功能
- 实现了技术指标计算功能（移动平均线、价格变化率、波动率）
- 实现了价格走势图表生成功能
- 实现了成交量和持仓量分析功能
- 创建了完整的单元测试套件 (test_apple_futures.py)
- 添加了数据可视化功能（matplotlib, seaborn）
- 生成了详细的分析报告功能
- **新增沪金2510期货数据获取程序 (gold_futures_data.py)**
- **创建了交互式HTML K线图页面 (gold_futures_kline.html)**
- **实现了沪金期货实时行情数据获取功能**
- **实现了沪金AU2510合约数据获取功能**
- **实现了沪金主力连续合约历史数据获取功能**
- **创建了K线数据格式转换功能，支持ECharts图表**
- **实现了JSON格式数据保存功能**
- **创建了沪金期货单元测试套件 (test_gold_futures.py)**
- **添加了响应式Web界面，支持移动端访问**
- **实现了多数据源切换功能（AU2510合约 vs 主力连续合约）**
- **添加了时间范围筛选功能**
- **集成了移动平均线技术指标（MA5、MA10、MA20）**
- **实现了成交量柱状图显示**
- **添加了数据缩放和拖拽功能**

### 变更
- 更新了 requirements.txt 包含所有必要依赖
- 完善了项目文档结构

### 修复
- 无

## [2025-08-05] - 项目初始化

### 新增
- 初始化项目结构
- 创建 Python 虚拟环境
- 配置开发环境

---

## 格式说明

本更新日志遵循 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/) 格式，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复
