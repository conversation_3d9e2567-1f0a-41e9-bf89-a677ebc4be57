# 更新日志

本文件记录项目的所有重要变更。

## [未发布]

### 新增
- 创建了 Python 虚拟环境 (venv)
- 配置了项目基础文件结构
- 添加了 .gitignore 文件以忽略虚拟环境和临时文件
- 创建了 requirements.txt 用于依赖管理
- 建立了项目文档结构

### 变更
- 无

### 修复
- 无

## [2025-08-05] - 项目初始化

### 新增
- 初始化项目结构
- 创建 Python 虚拟环境
- 配置开发环境

---

## 格式说明

本更新日志遵循 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/) 格式，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复
