# 更新日志

本文件记录项目的所有重要变更。

## [未发布]

### 新增
- 安装并配置了 akshare 数据获取库
- 创建了苹果期货数据获取程序 (apple_futures_data.py)
- 创建了苹果期货数据分析程序 (apple_futures_analysis.py)
- 实现了苹果期货实时行情数据获取功能
- 实现了苹果期货历史数据获取功能
- 实现了技术指标计算功能（移动平均线、价格变化率、波动率）
- 实现了价格走势图表生成功能
- 实现了成交量和持仓量分析功能
- 创建了完整的单元测试套件 (test_apple_futures.py)
- 添加了数据可视化功能（matplotlib, seaborn）
- 生成了详细的分析报告功能

### 变更
- 更新了 requirements.txt 包含所有必要依赖
- 完善了项目文档结构

### 修复
- 无

## [2025-08-05] - 项目初始化

### 新增
- 初始化项目结构
- 创建 Python 虚拟环境
- 配置开发环境

---

## 格式说明

本更新日志遵循 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/) 格式，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复
