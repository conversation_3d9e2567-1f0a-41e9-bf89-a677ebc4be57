<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沪金2510期货K线图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .control-group label {
            font-weight: bold;
            color: #495057;
        }
        
        .control-group select, .control-group button {
            padding: 8px 15px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            background-color: white;
            font-size: 14px;
        }
        
        .control-group button {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .control-group button:hover {
            background-color: #0056b3;
        }
        
        .chart-container {
            padding: 20px;
        }
        
        #klineChart {
            width: 100%;
            height: 600px;
        }
        
        .info-panel {
            padding: 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .info-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .info-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            #klineChart {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>沪金2510期货K线图</h1>
            <p>实时行情数据分析 | 上海期货交易所 AU2510</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="dataSource">数据源:</label>
                <select id="dataSource">
                    <option value="au2510">AU2510合约</option>
                    <option value="historical">主力连续合约</option>
                </select>
            </div>
            <div class="control-group">
                <label for="timeRange">时间范围:</label>
                <select id="timeRange">
                    <option value="all">全部</option>
                    <option value="30">最近30天</option>
                    <option value="60">最近60天</option>
                    <option value="90">最近90天</option>
                </select>
            </div>
            <div class="control-group">
                <button onclick="refreshData()">刷新数据</button>
            </div>
        </div>
        
        <div class="chart-container">
            <div id="klineChart"></div>
        </div>
        
        <div class="info-panel">
            <div class="info-grid">
                <div class="info-item">
                    <h4>当前价格</h4>
                    <div class="value" id="currentPrice">--</div>
                </div>
                <div class="info-item">
                    <h4>今日开盘</h4>
                    <div class="value" id="openPrice">--</div>
                </div>
                <div class="info-item">
                    <h4>今日最高</h4>
                    <div class="value" id="highPrice">--</div>
                </div>
                <div class="info-item">
                    <h4>今日最低</h4>
                    <div class="value" id="lowPrice">--</div>
                </div>
                <div class="info-item">
                    <h4>成交量</h4>
                    <div class="value" id="volume">--</div>
                </div>
                <div class="info-item">
                    <h4>数据更新时间</h4>
                    <div class="value" id="updateTime">--</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let chart = null;
        let allData = {};
        
        // 初始化图表
        function initChart() {
            const chartDom = document.getElementById('klineChart');
            chart = echarts.init(chartDom);
            
            // 显示加载动画
            chart.showLoading('default', {
                text: '正在加载数据...',
                color: '#007bff',
                textColor: '#000',
                maskColor: 'rgba(255, 255, 255, 0.8)',
                zlevel: 0
            });
        }
        
        // 加载数据
        async function loadData() {
            try {
                // 加载AU2510合约数据
                const au2510Response = await fetch('tempfiles/gold_futures_AU2510_kline.json');
                if (au2510Response.ok) {
                    allData.au2510 = await au2510Response.json();
                }
                
                // 加载主力连续合约数据
                const historicalResponse = await fetch('tempfiles/gold_futures_historical_kline.json');
                if (historicalResponse.ok) {
                    allData.historical = await historicalResponse.json();
                }
                
                // 更新图表
                updateChart();
                
            } catch (error) {
                console.error('加载数据失败:', error);
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '数据加载失败',
                        subtext: '请检查数据文件是否存在',
                        left: 'center',
                        top: 'center'
                    }
                });
            }
        }
        
        // 更新图表
        function updateChart() {
            const dataSource = document.getElementById('dataSource').value;
            const timeRange = document.getElementById('timeRange').value;
            
            let data = allData[dataSource] || [];
            
            // 根据时间范围过滤数据
            if (timeRange !== 'all' && data.length > 0) {
                const days = parseInt(timeRange);
                data = data.slice(-days);
            }
            
            if (data.length === 0) {
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '暂无数据',
                        left: 'center',
                        top: 'center'
                    }
                });
                return;
            }
            
            // 准备图表数据
            const dates = data.map(item => item[0]);
            const klineData = data.map(item => [item[1], item[2], item[3], item[4]]); // [开盘, 收盘, 最低, 最高]
            const volumeData = data.map(item => item[5]);
            
            // 计算移动平均线
            const ma5 = calculateMA(5, klineData);
            const ma10 = calculateMA(10, klineData);
            const ma20 = calculateMA(20, klineData);
            
            // 图表配置
            const option = {
                title: {
                    text: dataSource === 'au2510' ? '沪金AU2510合约K线图' : '沪金主力连续合约K线图',
                    left: 'center',
                    textStyle: {
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            if (param.seriesName === 'K线') {
                                const data = param.data;
                                result += `开盘: ${data[1]}<br/>`;
                                result += `收盘: ${data[2]}<br/>`;
                                result += `最低: ${data[3]}<br/>`;
                                result += `最高: ${data[4]}<br/>`;
                            } else if (param.seriesName === '成交量') {
                                result += `成交量: ${param.data}<br/>`;
                            } else {
                                result += `${param.seriesName}: ${param.data}<br/>`;
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['K线', 'MA5', 'MA10', 'MA20', '成交量'],
                    top: 30
                },
                grid: [
                    {
                        left: '10%',
                        right: '8%',
                        top: '15%',
                        height: '60%'
                    },
                    {
                        left: '10%',
                        right: '8%',
                        top: '80%',
                        height: '15%'
                    }
                ],
                xAxis: [
                    {
                        type: 'category',
                        data: dates,
                        scale: true,
                        boundaryGap: false,
                        axisLine: { onZero: false },
                        splitLine: { show: false },
                        min: 'dataMin',
                        max: 'dataMax'
                    },
                    {
                        type: 'category',
                        gridIndex: 1,
                        data: dates,
                        scale: true,
                        boundaryGap: false,
                        axisLine: { onZero: false },
                        axisTick: { show: false },
                        splitLine: { show: false },
                        axisLabel: { show: false },
                        min: 'dataMin',
                        max: 'dataMax'
                    }
                ],
                yAxis: [
                    {
                        scale: true,
                        splitArea: {
                            show: true
                        }
                    },
                    {
                        scale: true,
                        gridIndex: 1,
                        splitNumber: 2,
                        axisLabel: { show: false },
                        axisLine: { show: false },
                        axisTick: { show: false },
                        splitLine: { show: false }
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside',
                        xAxisIndex: [0, 1],
                        start: 50,
                        end: 100
                    },
                    {
                        show: true,
                        xAxisIndex: [0, 1],
                        type: 'slider',
                        top: '85%',
                        start: 50,
                        end: 100
                    }
                ],
                series: [
                    {
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: {
                            color: '#ef232a',
                            color0: '#14b143',
                            borderColor: '#ef232a',
                            borderColor0: '#14b143'
                        }
                    },
                    {
                        name: 'MA5',
                        type: 'line',
                        data: ma5,
                        smooth: true,
                        lineStyle: {
                            opacity: 0.8,
                            width: 1
                        }
                    },
                    {
                        name: 'MA10',
                        type: 'line',
                        data: ma10,
                        smooth: true,
                        lineStyle: {
                            opacity: 0.8,
                            width: 1
                        }
                    },
                    {
                        name: 'MA20',
                        type: 'line',
                        data: ma20,
                        smooth: true,
                        lineStyle: {
                            opacity: 0.8,
                            width: 1
                        }
                    },
                    {
                        name: '成交量',
                        type: 'bar',
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: volumeData,
                        itemStyle: {
                            color: function(params) {
                                const dataIndex = params.dataIndex;
                                if (dataIndex === 0) return '#14b143';
                                const current = klineData[dataIndex];
                                const previous = klineData[dataIndex - 1];
                                return current[1] > previous[1] ? '#ef232a' : '#14b143';
                            }
                        }
                    }
                ]
            };
            
            chart.hideLoading();
            chart.setOption(option, true);
            
            // 更新信息面板
            updateInfoPanel(data);
        }
        
        // 计算移动平均线
        function calculateMA(dayCount, data) {
            const result = [];
            for (let i = 0; i < data.length; i++) {
                if (i < dayCount - 1) {
                    result.push('-');
                    continue;
                }
                let sum = 0;
                for (let j = 0; j < dayCount; j++) {
                    sum += data[i - j][1]; // 使用收盘价
                }
                result.push((sum / dayCount).toFixed(2));
            }
            return result;
        }
        
        // 更新信息面板
        function updateInfoPanel(data) {
            if (data.length === 0) return;
            
            const latest = data[data.length - 1];
            
            document.getElementById('currentPrice').textContent = `${latest[2]} 元/克`;
            document.getElementById('openPrice').textContent = `${latest[1]} 元/克`;
            document.getElementById('highPrice').textContent = `${latest[4]} 元/克`;
            document.getElementById('lowPrice').textContent = `${latest[3]} 元/克`;
            document.getElementById('volume').textContent = latest[5].toLocaleString();
            document.getElementById('updateTime').textContent = new Date().toLocaleString();
        }
        
        // 刷新数据
        function refreshData() {
            chart.showLoading();
            loadData();
        }
        
        // 事件监听
        document.getElementById('dataSource').addEventListener('change', updateChart);
        document.getElementById('timeRange').addEventListener('change', updateChart);
        
        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', function() {
            if (chart) {
                chart.resize();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            loadData();
        });
    </script>
</body>
</html>
