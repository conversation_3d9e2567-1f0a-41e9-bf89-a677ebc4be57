# 沪金期货实时数据系统使用说明

## 系统概述

本系统实现了沪金期货的实时数据收集和可视化功能，包括：
- 每5分钟自动收集一次实时行情数据
- 生成交互式K线图HTML页面
- 页面每5分钟自动刷新显示最新数据

## 系统架构

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   数据收集器         │    │   数据文件           │    │   HTML前端页面       │
│                     │    │                     │    │                     │
│ • 每5分钟收集数据    │───▶│ • JSON格式存储       │───▶│ • 交互式K线图        │
│ • 模拟真实行情      │    │ • K线格式转换       │    │ • 自动刷新功能      │
│ • 价格连续性保证    │    │ • 数据量自动控制    │    │ • 多时间范围筛选    │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## 核心文件说明

### 1. 数据收集器
- **`gold_futures_realtime_simple.py`**: 核心数据收集器类
- **`start_realtime_collector.py`**: 启动脚本，提供友好的用户界面

### 2. 前端页面
- **`gold_futures_kline.html`**: 交互式K线图页面

### 3. 数据文件
- **`tempfiles/gold_futures_realtime_5min.json`**: 原始数据文件
- **`tempfiles/gold_futures_realtime_kline.json`**: K线格式数据
- **`tempfiles/gold_futures_realtime.log`**: 系统日志

### 4. 测试文件
- **`test_realtime_collector.py`**: 完整的单元测试套件

## 使用步骤

### 第一步：启动数据收集器

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动数据收集器
python start_realtime_collector.py
```

启动后会看到：
```
============================================================
沪金期货实时数据收集器
============================================================
✅ 数据收集器已启动
📊 每5分钟自动收集一次数据
🌐 HTML页面将每5分钟自动刷新
📁 数据文件: tempfiles/gold_futures_realtime_kline.json
📝 日志文件: tempfiles/gold_futures_realtime.log

状态监控:
------------------------------------------------------------
⏰ 2025-08-06 07:50:41 | 数据点: 15 | 当前价格: 760.26 元/克 | 最后更新: 2025-08-06 07:50:41
```

### 第二步：打开HTML页面

在浏览器中打开 `gold_futures_kline.html` 文件，即可看到实时K线图。

### 第三步：配置自动刷新

页面默认开启自动刷新功能，每5分钟自动更新数据。您可以：
- 使用"自动刷新"开关控制是否自动刷新
- 选择"实时5分钟数据"数据源查看最新数据
- 调整时间范围筛选（支持6小时、12小时、24小时等）

## 功能特性

### 数据收集功能
- **自动收集**: 每5分钟自动收集一次数据
- **数据模拟**: 生成真实的价格波动数据
- **价格连续性**: 确保价格变化合理，避免异常跳跃
- **数据限制**: 自动维护最近24小时的数据（288个数据点）
- **错误处理**: 完善的异常处理和日志记录

### 前端可视化功能
- **交互式K线图**: 使用ECharts专业图表库
- **多数据源**: 支持AU2510合约、主力连续合约、实时5分钟数据
- **技术指标**: MA5、MA10、MA20移动平均线
- **成交量分析**: 成交量柱状图，涨跌颜色区分
- **时间筛选**: 支持多种时间范围筛选
- **自动刷新**: 每5分钟自动更新数据
- **响应式设计**: 支持桌面和移动端访问

### 数据管理功能
- **多格式存储**: JSON原始数据 + K线格式数据
- **自动清理**: 超过限制的旧数据自动删除
- **日志记录**: 详细的操作日志和错误记录
- **状态监控**: 实时显示系统运行状态

## 技术参数

### 数据收集参数
- **收集频率**: 每5分钟
- **数据保留**: 最近24小时（288个数据点）
- **价格波动范围**: ±2%（单次）
- **价格总体范围**: 基础价格的90%-110%

### 前端刷新参数
- **自动刷新频率**: 每5分钟（300秒）
- **手动刷新**: 随时可用
- **数据加载**: 异步加载，不阻塞界面

## 故障排除

### 常见问题

1. **数据收集器无法启动**
   - 检查虚拟环境是否激活
   - 确认所有依赖包已安装：`pip install -r requirements.txt`

2. **HTML页面显示"数据加载失败"**
   - 确认数据收集器正在运行
   - 检查 `tempfiles` 目录是否存在数据文件

3. **自动刷新不工作**
   - 检查浏览器控制台是否有错误
   - 确认"自动刷新"开关已开启

4. **价格数据异常**
   - 查看日志文件：`tempfiles/gold_futures_realtime.log`
   - 重启数据收集器

### 日志查看

```bash
# 查看实时日志
tail -f tempfiles/gold_futures_realtime.log

# 查看最近的日志
tail -20 tempfiles/gold_futures_realtime.log
```

## 扩展功能

### 自定义配置

您可以修改以下参数来自定义系统行为：

```python
# 在 gold_futures_realtime_simple.py 中
self.max_data_points = 288  # 最大数据点数量
self.base_price = 782.50    # 基础价格

# 在 gold_futures_kline.html 中
5 * 60 * 1000  # 自动刷新间隔（毫秒）
```

### 添加新的技术指标

在HTML页面的 `calculateMA` 函数附近添加新的技术指标计算函数。

### 集成真实数据源

替换 `gold_futures_realtime_simple.py` 中的模拟数据生成函数，接入真实的期货数据API。

## 性能优化

- 数据文件大小自动控制，避免无限增长
- 前端使用异步加载，提高响应速度
- 图表使用数据缩放，支持大量数据点显示
- 内存使用优化，定期清理过期数据

## 安全注意事项

- 数据文件存储在本地，注意文件权限设置
- 日志文件可能包含敏感信息，定期清理
- 生产环境使用时，建议添加访问控制

## 技术支持

如有问题，请：
1. 查看日志文件获取详细错误信息
2. 运行单元测试检查系统状态：`python test_realtime_collector.py`
3. 检查系统资源使用情况
4. 重启数据收集器和浏览器页面
