#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪金2510期货数据获取程序
使用 akshare 库获取沪金2510期货的实时行情和历史数据

作者: 学习者
日期: 2025-08-05
"""

import akshare as ak
import pandas as pd
import datetime
import os
import json


def get_gold_futures_realtime():
    """
    获取沪金期货实时行情数据
    
    Returns:
        pandas.DataFrame: 沪金期货实时行情数据
    """
    try:
        print("正在获取沪金期货实时行情数据...")
        
        # 获取上海期货交易所实时行情数据
        futures_spot_df = ak.futures_zh_spot(symbol="上海期货交易所")
        
        # 筛选沪金期货数据（代码包含 AU 的合约）
        gold_futures = futures_spot_df[futures_spot_df['代码'].str.contains('AU', na=False)]
        
        print(f"获取到 {len(gold_futures)} 个沪金期货合约的实时数据")
        return gold_futures
        
    except Exception as e:
        print(f"获取实时数据时出错: {e}")
        return None


def get_gold_futures_au2510_data():
    """
    获取沪金2510合约的详细数据
    
    Returns:
        pandas.DataFrame: AU2510合约数据
    """
    try:
        print("正在获取沪金2510合约数据...")
        
        # 获取AU2510合约的日线数据
        au2510_data = ak.futures_zh_daily_sina(symbol="AU2510")
        
        print(f"获取到 {len(au2510_data)} 条AU2510合约数据")
        return au2510_data
        
    except Exception as e:
        print(f"获取AU2510合约数据时出错: {e}")
        return None


def get_gold_futures_historical():
    """
    获取沪金主力连续合约历史数据
    
    Returns:
        pandas.DataFrame: 沪金主力连续合约历史数据
    """
    try:
        print("正在获取沪金主力连续合约历史数据...")
        
        # 获取沪金主力连续合约历史数据（最近3个月）
        end_date = datetime.datetime.now().strftime("%Y%m%d")
        start_date = (datetime.datetime.now() - datetime.timedelta(days=90)).strftime("%Y%m%d")
        
        hist_data = ak.futures_main_sina(
            symbol="AU0", 
            start_date=start_date, 
            end_date=end_date
        )
        
        print(f"获取到 {len(hist_data)} 条历史数据")
        return hist_data
        
    except Exception as e:
        print(f"获取历史数据时出错: {e}")
        return None


def save_data_to_csv(data, filename):
    """
    将数据保存到 CSV 文件
    
    Args:
        data (pandas.DataFrame): 要保存的数据
        filename (str): 文件名
    """
    try:
        # 确保 tempfiles 目录存在
        os.makedirs('tempfiles', exist_ok=True)
        
        filepath = os.path.join('tempfiles', filename)
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filepath}")
        
    except Exception as e:
        print(f"保存数据时出错: {e}")


def prepare_kline_data(data):
    """
    准备K线图数据，转换为前端可用的格式
    
    Args:
        data (pandas.DataFrame): 原始数据
        
    Returns:
        list: K线图数据
    """
    try:
        if data is None or data.empty:
            return []
        
        kline_data = []
        
        # 确定列名（处理不同数据源的列名差异）
        date_col = 'date' if 'date' in data.columns else '日期'
        open_col = 'open' if 'open' in data.columns else '开盘价'
        high_col = 'high' if 'high' in data.columns else '最高价'
        low_col = 'low' if 'low' in data.columns else '最低价'
        close_col = 'close' if 'close' in data.columns else '收盘价'
        volume_col = 'volume' if 'volume' in data.columns else '成交量'
        
        for _, row in data.iterrows():
            # 转换日期格式
            if isinstance(row[date_col], str):
                date_str = row[date_col]
            else:
                date_str = row[date_col].strftime('%Y-%m-%d')
            
            # K线数据格式: [日期, 开盘价, 收盘价, 最低价, 最高价, 成交量]
            kline_item = [
                date_str,
                float(row[open_col]) if pd.notna(row[open_col]) else 0,
                float(row[close_col]) if pd.notna(row[close_col]) else 0,
                float(row[low_col]) if pd.notna(row[low_col]) else 0,
                float(row[high_col]) if pd.notna(row[high_col]) else 0,
                int(row[volume_col]) if pd.notna(row[volume_col]) else 0
            ]
            kline_data.append(kline_item)
        
        # 按日期排序
        kline_data.sort(key=lambda x: x[0])
        
        print(f"准备了 {len(kline_data)} 条K线数据")
        return kline_data
        
    except Exception as e:
        print(f"准备K线数据时出错: {e}")
        return []


def save_kline_data_json(kline_data, filename):
    """
    将K线数据保存为JSON格式
    
    Args:
        kline_data (list): K线数据
        filename (str): 文件名
    """
    try:
        os.makedirs('tempfiles', exist_ok=True)
        filepath = os.path.join('tempfiles', filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(kline_data, f, ensure_ascii=False, indent=2)
        
        print(f"K线数据已保存到: {filepath}")
        
    except Exception as e:
        print(f"保存K线数据时出错: {e}")


def display_data_summary(data, title):
    """
    显示数据摘要信息
    
    Args:
        data (pandas.DataFrame): 数据
        title (str): 标题
    """
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    
    if data is not None and not data.empty:
        print(f"数据行数: {len(data)}")
        print(f"数据列数: {len(data.columns)}")
        print(f"\n列名: {list(data.columns)}")
        print(f"\n前5行数据:")
        print(data.head())
        
        # 如果有价格列，显示价格统计信息
        price_cols = [col for col in data.columns if any(keyword in col.lower() for keyword in ['price', '价格', 'close', '收盘'])]
        if price_cols:
            price_col = price_cols[0]
            current_price = data[price_col].iloc[-1] if len(data) > 0 else 0
            max_price = data[price_col].max()
            min_price = data[price_col].min()
            avg_price = data[price_col].mean()
            
            print(f"\n价格统计信息:")
            print(f"当前价格: {current_price:.2f} 元/克")
            print(f"最高价格: {max_price:.2f} 元/克")
            print(f"最低价格: {min_price:.2f} 元/克")
            print(f"平均价格: {avg_price:.2f} 元/克")
    else:
        print("数据为空或获取失败")


def main():
    """
    主函数：获取沪金2510期货数据
    """
    print("沪金2510期货数据获取程序启动")
    print("="*60)
    
    # 1. 获取实时行情数据
    realtime_data = get_gold_futures_realtime()
    if realtime_data is not None:
        display_data_summary(realtime_data, "沪金期货实时行情数据")
        save_data_to_csv(realtime_data, "gold_futures_realtime.csv")
    
    # 2. 获取AU2510合约数据
    au2510_data = get_gold_futures_au2510_data()
    if au2510_data is not None:
        display_data_summary(au2510_data, "沪金AU2510合约数据")
        save_data_to_csv(au2510_data, "gold_futures_AU2510.csv")
        
        # 准备K线数据
        kline_data = prepare_kline_data(au2510_data)
        if kline_data:
            save_kline_data_json(kline_data, "gold_futures_AU2510_kline.json")
    
    # 3. 获取主力连续合约历史数据
    historical_data = get_gold_futures_historical()
    if historical_data is not None:
        display_data_summary(historical_data, "沪金主力连续合约历史数据")
        save_data_to_csv(historical_data, "gold_futures_historical.csv")
        
        # 准备K线数据
        kline_data_hist = prepare_kline_data(historical_data)
        if kline_data_hist:
            save_kline_data_json(kline_data_hist, "gold_futures_historical_kline.json")
    
    print("\n" + "="*60)
    print("沪金期货数据获取完成！所有文件已保存到 tempfiles 目录")
    print("="*60)


if __name__ == "__main__":
    main()
